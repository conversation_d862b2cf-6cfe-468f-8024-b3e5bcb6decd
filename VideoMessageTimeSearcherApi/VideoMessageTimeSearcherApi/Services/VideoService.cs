using AutoMapper;
using VideoMessageTimeSearcherApi.DTOs;
using VideoMessageTimeSearcherApi.Models;
using VideoMessageTimeSearcherApi.Repositories.Interfaces;
using VideoMessageTimeSearcherApi.Services.Interfaces;

namespace VideoMessageTimeSearcherApi.Services
{
    public class VideoService : IVideoService
    {
        private readonly IVideoRepository _videoRepository;
        private readonly IMapper _mapper;

        public VideoService(IVideoRepository videoRepository, IMapper mapper)
        {
            _videoRepository = videoRepository;
            _mapper = mapper;
        }

        public async Task<PagedResult<VideoDto>> SearchVideosAsync(SearchVideosRequest request)
        {
            // Validate request
            if (request.PageNumber < 1) request.PageNumber = 1;
            if (request.PageSize < 1 || request.PageSize > 100) request.PageSize = 10;

            // Call repository
            var result = await _videoRepository.SearchVideosAsync(request);

            // Map to DTOs
            var mappedItems = _mapper.Map<List<VideoDto>>(result.Items);

            return new PagedResult<VideoDto>
            {
                Items = mappedItems,
                TotalCount = result.TotalCount,
                PageNumber = result.PageNumber,
                PageSize = result.PageSize
            };
        }


        public async Task<List<WordOccurrenceDto>> SearchTranscriptsAsync(SearchTranscriptRequest request)
        {
            if (string.IsNullOrWhiteSpace(request.SearchText))
            {
                return new List<WordOccurrenceDto>();
            }

            // Trim and normalize the search text
            request.SearchText = request.SearchText.Trim();

            return await _videoRepository.SearchTranscriptsAsync(request);
        }

        #region Video Operations

        public async Task<VideoDto> GetVideoByIdAsync(int id)
        {
            var video = await _videoRepository.GetVideoByIdAsync(id);
            return video == null ? null : _mapper.Map<VideoDto>(video);
        }

        public async Task<VideoDto> CreateVideoAsync(CreateVideoRequest request)
        {
            var video = _mapper.Map<Video>(request);
            var createdVideo = await _videoRepository.CreateVideoAsync(video);
            return _mapper.Map<VideoDto>(createdVideo);
        }

        public async Task<bool> UpdateVideoAsync(int id, CreateVideoRequest request)
        {
            var video = await _videoRepository.GetVideoByIdAsync(id);
            if (video == null) return false;

            _mapper.Map(request, video);
            return await _videoRepository.UpdateVideoAsync(video);
        }

        public async Task<bool> DeleteVideoAsync(int id)
        {
            return await _videoRepository.DeleteVideoAsync(id);
        }

        #endregion


    }
}
