using AutoMapper;
using VideoMessageTimeSearcherApi.DTOs;
using VideoMessageTimeSearcherApi.Models;

namespace VideoMessageTimeSearcherApi.Profiles
{
    public class AutoMapperProfile : Profile
    {
        public AutoMapperProfile()
        {
            // Video mappings
            CreateMap<Video, VideoDto>();
            CreateMap<CreateVideoRequest, Video>();
            
            // Segment group mappings
            CreateMap<TranscriptSegmentGroup, WordOccurrenceDto>()
                .ForMember(dest => dest.VideoTitle, opt => opt.MapFrom(src => src.Video.Title));
                
            // Word timing mappings
            CreateMap<WordTiming, WordTimingDto>();
        }
    }
}
