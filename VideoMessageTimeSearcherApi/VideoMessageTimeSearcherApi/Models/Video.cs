using System;
using System.Collections.Generic;

namespace VideoMessageTimeSearcherApi.Models
{
    public class Video
    {
        public int Id { get; set; }
        public string Title { get; set; }
        public double DurationSeconds { get; set; }
        public string FilePath { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        
        // Navigation properties
        public ICollection<TranscriptSegmentGroup> TranscriptSegmentGroups { get; set; }
    }
}
