using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using VideoMessageTimeSearcherApi.Models;
using VideoMessageTimeSearcherApi.Models.Identity;

namespace VideoMessageTimeSearcherApi.DbContexts
{
    public class AppDbContext : IdentityDbContext<User, IdentityRole, string>
    {
        public AppDbContext(DbContextOptions<AppDbContext> options) : base(options)
        {
        }

        public DbSet<Video> Videos { get; set; }
        public DbSet<TranscriptSegmentGroup> TranscriptSegmentGroups { get; set; }
        public DbSet<TranscriptSegment> TranscriptSegments { get; set; }
        public DbSet<WordTiming> WordTimings { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Rename the Identity tables to use singular names
            foreach (var entityType in modelBuilder.Model.GetEntityTypes())
            {
                var tableName = entityType.GetTableName();
                if (tableName != null && tableName.StartsWith("AspNet"))
                {
                    entityType.SetTableName(tableName[6..]);
                }
            }

            // Configure Video entity
            modelBuilder.Entity<Video>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Title).IsRequired();
                entity.Property(e => e.DurationSeconds).IsRequired();
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
                entity.Property(e => e.FilePath).IsRequired();
            });

            // Configure TranscriptSegmentGroup entity
            modelBuilder.Entity<TranscriptSegmentGroup>(entity =>
            {
                entity.ToTable(tb => 
                    tb.HasCheckConstraint("CK_TranscriptSegmentGroups_TimeRange", 
                        "\"StartTime\" >= 0 AND \"EndTime\" >= \"StartTime\""));

                entity.HasKey(e => e.Id);
                
                entity.HasOne(e => e.Video)
                    .WithMany(v => v.TranscriptSegmentGroups)
                    .HasForeignKey(e => e.VideoId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.Property(e => e.StartTime).IsRequired();
                entity.Property(e => e.EndTime).IsRequired();
            });

            // Configure TranscriptSegment entity
            modelBuilder.Entity<TranscriptSegment>(entity =>
            {
                entity.HasKey(e => e.Id);
                
                entity.HasOne(e => e.Group)
                    .WithMany(g => g.TranscriptSegments)
                    .HasForeignKey(e => e.GroupId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.Property(e => e.LanguageCode).IsRequired().HasMaxLength(10);
                entity.Property(e => e.Text).IsRequired();

                entity.HasIndex(e => new { e.GroupId, e.LanguageCode })
                    .IsUnique();
            });

            // Configure WordTiming entity
            modelBuilder.Entity<WordTiming>(entity =>
            {
                entity.ToTable(tb =>
                    tb.HasCheckConstraint("CK_WordTimings_TimeRange",
                        "\"StartTime\" >= 0 AND \"EndTime\" >= \"StartTime\""));

                entity.HasKey(e => e.Id);
                
                entity.HasOne(w => w.Segment)
                      .WithMany(s => s.WordTimings)
                      .HasForeignKey(w => w.SegmentId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.Property(e => e.Word).IsRequired();
                entity.Property(e => e.StartTime).IsRequired();
                entity.Property(e => e.EndTime).IsRequired();
                entity.Property(e => e.WordIndex).IsRequired();
            });
        }
    }
}
